#Requires -Version 5.1

<#
.SYNOPSIS
    Continuous network monitoring script with logging and traceroute functionality.

.DESCRIPTION
    This script continuously monitors a specified IP address by pinging it every 10 seconds.
    It logs failures when RTT > 100ms or when the host is unreachable, and performs
    traceroute analysis for unreachable hosts.

.PARAMETER IPAddress
    The IP address to monitor. Defaults to ******* (Google DNS).

.PARAMETER LogFile
    Path to the log file. Defaults to PsCheckLogInt.log in the current directory.

.PARAMETER RTTThreshold
    Round trip time threshold in milliseconds. Defaults to 100ms.

.PARAMETER PingInterval
    Interval between pings in seconds. Defaults to 10 seconds.

.EXAMPLE
    .\PsCheckLogInt.ps1 -IPAddress "***********"
    
.EXAMPLE
    .\PsCheckLogInt.ps1 -IPAddress "*******" -LogFile "C:\Logs\network.log" -RTTThreshold 50
#>

param(
    [Parameter(Mandatory = $false)]
    [ValidateScript({
        if ($_ -match '^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$') {
            $true
        } else {
            throw "Invalid IP address format. Please provide a valid IPv4 address."
        }
    })]
    [string]$IPAddress = "*******",
    
    [Parameter(Mandatory = $false)]
    [string]$LogFile = "PsCheckLogInt.log",
    
    [Parameter(Mandatory = $false)]
    [ValidateRange(1, 10000)]
    [int]$RTTThreshold = 100,
    
    [Parameter(Mandatory = $false)]
    [ValidateRange(1, 300)]
    [int]$PingInterval = 10
)

# Function to write log entries with timestamp
function Write-LogEntry {
    param(
        [string]$Message,
        [string]$LogPath,
        [switch]$AddSeparator
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] $Message"
    
    try {
        if ($AddSeparator) {
            Add-Content -Path $LogPath -Value "`n$('-' * 80)" -ErrorAction Stop
        }
        Add-Content -Path $LogPath -Value $logEntry -ErrorAction Stop
        Write-Host $logEntry -ForegroundColor Yellow
    }
    catch {
        Write-Error "Failed to write to log file: $_"
    }
}

# Function to perform traceroute and log results
function Invoke-TracerouteAndLog {
    param(
        [string]$TargetIP,
        [string]$LogPath
    )
    
    Write-LogEntry -Message "TRACEROUTE ANALYSIS: Starting traceroute to $TargetIP" -LogPath $LogPath
    
    try {
        # Execute tracert command and capture output
        $tracertOutput = & tracert $TargetIP 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-LogEntry -Message "Traceroute completed successfully:" -LogPath $LogPath
        } else {
            Write-LogEntry -Message "Traceroute completed with warnings/errors:" -LogPath $LogPath
        }
        
        # Log each line of traceroute output
        foreach ($line in $tracertOutput) {
            Add-Content -Path $LogPath -Value "  $line"
        }
        
        Write-LogEntry -Message "TRACEROUTE ANALYSIS: Completed for $TargetIP" -LogPath $LogPath -AddSeparator
    }
    catch {
        Write-LogEntry -Message "ERROR: Failed to execute traceroute: $_" -LogPath $LogPath
    }
}

# Function to test network connectivity
function Test-NetworkConnectivity {
    param(
        [string]$TargetIP,
        [int]$TimeoutMs = 5000
    )
    
    try {
        # Use Test-NetConnection for more detailed information
        $result = Test-NetConnection -ComputerName $TargetIP -InformationLevel Detailed -WarningAction SilentlyContinue
        
        return @{
            Success = $result.PingSucceeded
            RTT = if ($result.PingReplyDetails) { $result.PingReplyDetails.RoundtripTime } else { $null }
            Status = if ($result.PingSucceeded) { "Success" } else { "Failed" }
            Details = $result
        }
    }
    catch {
        # Fallback to basic ping if Test-NetConnection fails
        try {
            $pingResult = Test-Connection -ComputerName $TargetIP -Count 1 -Quiet -ErrorAction Stop
            return @{
                Success = $pingResult
                RTT = $null
                Status = if ($pingResult) { "Success" } else { "Failed" }
                Details = "Fallback ping result"
            }
        }
        catch {
            return @{
                Success = $false
                RTT = $null
                Status = "Error"
                Details = $_.Exception.Message
            }
        }
    }
}

# Main script execution
try {
    # Ensure log file directory exists
    $logDir = Split-Path -Path $LogFile -Parent
    if ($logDir -and -not (Test-Path -Path $logDir)) {
        New-Item -ItemType Directory -Path $logDir -Force | Out-Null
    }
    
    # Initialize log file with script start information
    $startMessage = @"
================================================================================
NETWORK MONITORING SESSION STARTED
================================================================================
Target IP Address: $IPAddress
RTT Threshold: $RTTThreshold ms
Ping Interval: $PingInterval seconds
Log File: $LogFile
Script Started: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
================================================================================
"@
    
    Add-Content -Path $LogFile -Value $startMessage
    
    Write-Host "Network Monitoring Started" -ForegroundColor Green
    Write-Host "Target IP: $IPAddress" -ForegroundColor Cyan
    Write-Host "RTT Threshold: $RTTThreshold ms" -ForegroundColor Cyan
    Write-Host "Ping Interval: $PingInterval seconds" -ForegroundColor Cyan
    Write-Host "Log File: $LogFile" -ForegroundColor Cyan
    Write-Host "Press Ctrl+C to stop monitoring`n" -ForegroundColor Yellow
    
    # Continuous monitoring loop
    while ($true) {
        $pingResult = Test-NetworkConnectivity -TargetIP $IPAddress

        if (-not $pingResult.Success) {
            # Host unreachable - log and perform traceroute
            Write-LogEntry -Message "PING FAILURE: Host $IPAddress is unreachable" -LogPath $LogFile
            Write-LogEntry -Message "Status: $($pingResult.Status)" -LogPath $LogFile
            Write-LogEntry -Message "Details: $($pingResult.Details)" -LogPath $LogFile

            # Perform traceroute analysis
            Invoke-TracerouteAndLog -TargetIP $IPAddress -LogPath $LogFile

        } elseif ($pingResult.RTT -and $pingResult.RTT -gt $RTTThreshold) {
            # High latency detected - log and perform traceroute
            Write-LogEntry -Message "HIGH LATENCY: Ping to $IPAddress exceeded threshold" -LogPath $LogFile
            Write-LogEntry -Message "RTT: $($pingResult.RTT) ms (Threshold: $RTTThreshold ms)" -LogPath $LogFile
            Write-LogEntry -Message "Status: Response received but latency is high" -LogPath $LogFile

            # Perform traceroute analysis for high latency
            Invoke-TracerouteAndLog -TargetIP $IPAddress -LogPath $LogFile

        } else {
            # Successful ping within threshold
            $rttDisplay = if ($pingResult.RTT) { "$($pingResult.RTT) ms" } else { "N/A" }
            Write-Host "$(Get-Date -Format "HH:mm:ss") - Ping to $IPAddress successful (RTT: $rttDisplay)" -ForegroundColor Green
        }
        
        # Wait for next ping interval
        Start-Sleep -Seconds $PingInterval
    }
}
catch {
    $errorMessage = "SCRIPT ERROR: $($_.Exception.Message)"
    Write-LogEntry -Message $errorMessage -LogPath $LogFile
    Write-Error $errorMessage
    exit 1
}
finally {
    # Log script termination
    $endMessage = @"

================================================================================
NETWORK MONITORING SESSION ENDED
================================================================================
Session Ended: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
================================================================================
"@
    
    try {
        Add-Content -Path $LogFile -Value $endMessage
    }
    catch {
        Write-Warning "Could not write session end to log file"
    }
    
    Write-Host "`nNetwork monitoring stopped." -ForegroundColor Red
}
